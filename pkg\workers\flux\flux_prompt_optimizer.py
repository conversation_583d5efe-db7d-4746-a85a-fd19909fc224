"""
[二次开发] Flux 提示词优化模块
负责LLM润色、提示词增强等（专用于aigen/ControlNet/LoRA等Flux管线）

开发说明：
- 此文件为二次开发代码，不属于langbot原生代码
- 功能：Flux工作流的提示词优化和LLM翻译
- 维护者：开发团队
- 最后更新：2024-12-20
- 相关任务：REQ-01 需求范围定义
- 依赖关系：依赖langbot的LLM模型管理器和查询对象
"""
import re
import logging
from typing import Dict, Optional, Any

class FluxPromptOptimizer:
    """
    LLM提示词优化与增强（Flux专用）
    """
    def __init__(self, lora_dict: Optional[Dict[str, str]] = None):
        self.lora_dict = lora_dict or {}
        self.logger = logging.getLogger(__name__)

    def optimize_prompt(self, prompt: str) -> str:
        """
        对输入的prompt进行优化（如润色、去噪、增强等）
        """
        if not prompt or not prompt.strip():
            return prompt

        # 基础清理
        cleaned_prompt = self._clean_prompt(prompt)

        # 简单的英文优化（如果是中文，建议使用LLM翻译）
        if self._is_chinese(cleaned_prompt):
            # 如果是中文，返回原文（需要LLM翻译）
            self.logger.info("检测到中文提示词，建议使用LLM翻译")
            return cleaned_prompt

        # 英文提示词优化
        optimized = self._enhance_english_prompt(cleaned_prompt)
        return optimized

    def _clean_prompt(self, prompt: str) -> str:
        """清理提示词：保留中文标点，仅清理英文特殊符号"""
        cleaned = prompt.strip()
        # 检查是否包含中文
        if self._is_chinese(cleaned):
            # 仅移除英文特殊符号，保留中文标点
            # 中文标点范围：\u3000-\u303F，\uFF00-\uFFEF
            cleaned = re.sub(r'[A-Za-z0-9@#\$%\^&\*_+=<>\|~`]', '', cleaned)
            # 不移除中文逗号、句号、顿号等
        else:
            # 英文场景下移除特殊字符（保留基本标点）
            cleaned = re.sub(r'[^\w\s\-.,!?()[\]{}:;"\']', '', cleaned)
        return cleaned

    def _is_chinese(self, text: str) -> bool:
        """检测是否包含中文"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))

    def _enhance_english_prompt(self, prompt: str) -> str:
        """增强英文提示词（Flux风格）"""
        # Flux偏好高质量、细节、艺术风格
        enhanced = prompt
        quality_keywords = ['high quality', 'detailed', 'masterpiece', 'best quality', '8k', 'ultra detailed']
        if not any(keyword in enhanced.lower() for keyword in quality_keywords):
            enhanced = f"masterpiece, best quality, ultra detailed, {enhanced}"
        # 确保结尾有标点
        enhanced = enhanced.strip()
        if not enhanced.endswith('.'):
            enhanced += '.'
        return enhanced

    async def optimize_prompt_with_llm(self, prompt: str, query: Any) -> str:
        """
        使用LLM优化提示词（完整版本，Flux专用系统提示词和示例）
        """
        try:
            if self._is_chinese(prompt):
                return await self._translate_and_optimize(prompt, query)
            else:
                return await self._optimize_english_with_llm(prompt, query)
        except Exception as e:
            self.logger.error(f"LLM提示词优化失败: {e}")
            return self.optimize_prompt(prompt)

    async def _translate_and_optimize(self, chinese_prompt: str, query: Any) -> str:
        """
        翻译中文并优化（Flux专用系统提示词，采用自然语言+专业修饰词最佳实践）
        """
        try:
            system_prompt = '''You are a professional AI prompt optimizer specializing in Stable Diffusion, ControlNet, and LoRA workflows. Please convert the user's Chinese description into a high-quality English prompt using fluent, natural language sentences. Your prompt should:

- Clearly describe the subject (person, animal, building, object, etc.)
- Include actions or states
- Specify the environment and background (city, nature, interior, etc.)
- Convey atmosphere and emotion (e.g., warm, mysterious, futuristic)
- Indicate artistic style or medium (oil painting, photography, digital illustration, architectural rendering, etc.)
- Add details about lighting, color, composition, camera angle, etc.
- Optionally reference famous artists, architects, or photographers
- Optionally include negative prompts (elements to avoid)

**Requirements:**
1. Use natural language, not just a list of keywords.
2. Add rich modifiers and highlight scene details and mood.
3. Limit to 200 words.
4. Only return the optimized English prompt, nothing else.

**Examples:**
1. Input: 现代风格的办公楼，玻璃幕墙，夜晚灯光明亮，前景有绿植
   Output: A modern office building with glass curtain walls, illuminated brightly at night. Lush greenery decorates the entrance, the atmosphere is vibrant and energetic, photographed with a wide-angle lens, soft reflections on the glass, minimalist architectural style.

2. Input: 一个小女孩在公园里放风筝，阳光明媚，草地翠绿
   Output: A little girl flying a kite in a sunlit park, lush green grass beneath her feet, joyful expression on her face, gentle breeze, soft natural lighting, cheerful and lively atmosphere, candid photography style.

3. Input: 未来城市的鸟瞰图，高楼林立，空中有飞行汽车
   Output: A futuristic cityscape viewed from above, towering skyscrapers, flying cars gliding through the sky, neon lights reflecting off glass buildings, dynamic composition, cinematic perspective, inspired by sci-fi concept art.

4. Input: 一只橘猫趴在窗台上，外面下着雨
   Output: An orange cat lying on a windowsill, gazing outside as rain falls, cozy indoor setting, soft diffused light, peaceful and warm mood, photorealistic style.

5. Input: 抽象的彩色几何图形，充满动感
   Output: Abstract colorful geometric shapes, dynamic composition, vibrant hues, sense of movement, modern art style, high contrast, digital illustration.'''
            user_prompt = f"请优化这个中文提示词：{chinese_prompt}"
            optimized = await self._call_llm_for_optimization(system_prompt, user_prompt, query)
            if optimized:
                return optimized.strip()
            else:
                return self._prompt_for_english(chinese_prompt)
        except Exception as e:
            self.logger.error(f"翻译优化失败: {e}")
            return self._prompt_for_english(chinese_prompt)

    async def _optimize_english_with_llm(self, english_prompt: str, query: Any) -> str:
        """
        优化英文提示词（Flux专用系统提示词，采用自然语言+专业修饰词最佳实践）
        """
        try:
            system_prompt = '''You are a professional AI prompt optimizer specializing in Stable Diffusion, ControlNet, and LoRA workflows. Please optimize the user's English prompt using fluent, natural language sentences. Your prompt should:

- Clearly describe the subject (person, animal, building, object, etc.)
- Include actions or states
- Specify the environment and background (city, nature, interior, etc.)
- Convey atmosphere and emotion (e.g., warm, mysterious, futuristic)
- Indicate artistic style or medium (oil painting, photography, digital illustration, architectural rendering, etc.)
- Add details about lighting, color, composition, camera angle, etc.
- Optionally reference famous artists, architects, or photographers
- Optionally include negative prompts (elements to avoid)

**Requirements:**
1. Use natural language, not just a list of keywords.
2. Add rich modifiers and highlight scene details and mood.
3. Limit to 200 words.
4. Only return the optimized English prompt, nothing else.

**Examples:**
1. A modern office building with glass curtain walls, illuminated brightly at night. Lush greenery decorates the entrance, the atmosphere is vibrant and energetic, photographed with a wide-angle lens, soft reflections on the glass, minimalist architectural style.
2. A little girl flying a kite in a sunlit park, lush green grass beneath her feet, joyful expression on her face, gentle breeze, soft natural lighting, cheerful and lively atmosphere, candid photography style.
3. A futuristic cityscape viewed from above, towering skyscrapers, flying cars gliding through the sky, neon lights reflecting off glass buildings, dynamic composition, cinematic perspective, inspired by sci-fi concept art.
4. An orange cat lying on a windowsill, gazing outside as rain falls, cozy indoor setting, soft diffused light, peaceful and warm mood, photorealistic style.
5. Abstract colorful geometric shapes, dynamic composition, vibrant hues, sense of movement, modern art style, high contrast, digital illustration.'''
            user_prompt = f"请优化这个英文提示词：{english_prompt}"
            optimized = await self._call_llm_for_optimization(system_prompt, user_prompt, query)
            if optimized:
                return optimized.strip()
            else:
                return self._enhance_english_prompt(english_prompt)
        except Exception as e:
            self.logger.error(f"英文优化失败: {e}")
            return self._enhance_english_prompt(english_prompt)

    async def _call_llm_for_optimization(self, system_prompt: str, user_prompt: str, query: Any) -> Optional[str]:
        """调用LLM进行优化"""
        try:
            # 详细的配置检查和日志
            if not query:
                self.logger.warning("❌ query对象为空，无法调用LLM")
                return None

            if not hasattr(query, 'pipeline_config'):
                self.logger.warning("❌ query对象缺少pipeline_config属性，无法调用LLM")
                return None

            if not query.pipeline_config:
                self.logger.warning("❌ query.pipeline_config为空，无法调用LLM")
                self.logger.info(f"🔍 query对象属性: {[attr for attr in dir(query) if not attr.startswith('_')]}")
                return None

            self.logger.info(f"✅ pipeline_config存在，类型: {type(query.pipeline_config)}")
            self.logger.info(f"🔍 pipeline_config内容: {query.pipeline_config}")
            model_uuid = query.pipeline_config.get('ai', {}).get('local-agent', {}).get('model', '')
            if not model_uuid:
                self.logger.warning("❌ 未配置LLM模型，无法进行提示词优化")
                self.logger.info(f"🔍 ai配置: {query.pipeline_config.get('ai', {})}")
                return None

            self.logger.info(f"✅ 找到模型UUID: {model_uuid}")
            if not hasattr(query, 'ap') or not query.ap:
                self.logger.warning("❌ 无法获取应用实例，无法调用LLM")
                self.logger.info(f"🔍 query.ap存在: {hasattr(query, 'ap')}, 值: {getattr(query, 'ap', None)}")
                return None

            self.logger.info(f"✅ 应用实例存在，model_mgr: {query.ap.model_mgr is not None}")
            runtime_llm_model = None
            for model in query.ap.model_mgr.llm_models:
                if model.model_entity.uuid == model_uuid:
                    runtime_llm_model = model
                    break
            if not runtime_llm_model:
                self.logger.warning(f"未找到模型 {model_uuid}，无法进行提示词优化")
                return None
            from ...provider import entities as llm_entities
            messages = [
                llm_entities.Message(role='system', content=system_prompt),
                llm_entities.Message(role='user', content=user_prompt)
            ]
            result = await runtime_llm_model.requester.invoke_llm(
                query,
                runtime_llm_model,
                messages,
                [],
                extra_args={},
            )
            response_text = self._extract_response_text(result)
            if response_text:
                self.logger.info("LLM提示词优化成功")
                return response_text.strip()
            else:
                self.logger.warning("LLM返回空响应")
                return None
        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            return None

    def _extract_response_text(self, result) -> str:
        """提取LLM响应文本"""
        response_text = ""
        if hasattr(result, 'content') and result.content:
            if isinstance(result.content, list):
                for element in result.content:
                    if hasattr(element, 'text') and element.text:
                        response_text += element.text
            elif isinstance(result.content, str):
                response_text = result.content
            else:
                response_text = str(result.content)
        return response_text.strip()

    def _prompt_for_english(self, chinese_prompt: str) -> str:
        """提示用户使用英文提示词"""
        return f"⚠️ LLM提示词优化暂时不可用，请直接使用英文提示词。\n\n💡 建议：您可以在前端配置页面检查并保存模型配置信息。\n\n原始输入：{chinese_prompt}"

    def apply_lora(self, prompt: str, lora_name: str) -> str:
        """
        根据lora_name将LoRA模型指令插入到prompt中
        """
        lora_tag = self.lora_dict.get(lora_name, f'<lora:{lora_name}>')
        return f"{lora_tag} {prompt}"

    def analyze_prompt(self, prompt: str) -> Dict[str, str]:
        """
        分析prompt，提取关键信息（如风格、主题等）
        """
        analysis = {
            "length": str(len(prompt)),
            "language": "chinese" if self._is_chinese(prompt) else "english",
            "has_quality_keywords": str(any(kw in prompt.lower() for kw in ['high quality', 'detailed', 'masterpiece', 'ultra detailed'])),
            "word_count": str(len(prompt.split()))
        }
        return analysis 