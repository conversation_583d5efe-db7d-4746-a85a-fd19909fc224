version: '3.8'

services:
  wechatpadpro:
    image: wechatpadpro/wechatpadpro:${WECHAT_TAG:-latest}
    container_name: wechatpadpro
    restart: always
    ports:
      - "${PORT:-1238}:1238"
    env_file:
      - .env
    environment:
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - MYSQL_CONNECT_STR=weixin:123456@tcp(mysql:3306)/weixin?charset=utf8mb4&parseTime=true&loc=Local
    volumes:
      - ./.env:/app/.env
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - wechatpadpro-network

  mysql:
    image: wechatpadpro/mysql:8.0
    container_name: mysql
    restart: always
    env_file:
      - .env
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-root123456}"]
      interval: 5s
      timeout: 5s
      retries: 20
    networks:
      - wechatpadpro-network

  redis:
    image: wechatpadpro/redis:6
    container_name: redis
    restart: always
    env_file:
      - .env
    volumes:
      - redis_data:/data
    command: ["redis-server", "--appendonly", "yes", "--requirepass", "${REDIS_PASSWORD:-123456}", "--maxmemory", "512mb", "--maxmemory-policy", "volatile-lru"]
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-123456}", "ping"]
      interval: 5s
      timeout: 5s
      retries: 20
    networks:
      - wechatpadpro-network

networks:
  wechatpadpro-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
