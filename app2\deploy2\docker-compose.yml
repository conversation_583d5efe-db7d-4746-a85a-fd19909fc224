version: '3.8'

services:
  wechatpadpro2:
    image: wechatpadpro/wechatpadpro:${WECHAT_TAG:-latest}
    container_name: wechatpadpro2
    restart: always
    ports:
      - "${PORT:-1239}:1238"
    env_file:
      - .env2
    environment:
      - DB_HOST=mysql2
      - REDIS_HOST=redis2
      - MYSQL_CONNECT_STR=weixin:123456@tcp(mysql2:3306)/weixin2?charset=utf8mb4&parseTime=true&loc=Local
    volumes:
      - ./.env2:/app/.env
    depends_on:
      mysql2:
        condition: service_healthy
      redis2:
        condition: service_healthy
    networks:
      - wechatpadpro2-network

  mysql2:
    image: wechatpadpro/mysql:8.0
    container_name: mysql2
    restart: always
    env_file:
      - .env2
    volumes:
      - mysql2_data:/var/lib/mysql
    ports:
      - "${MYSQL_PORT:-3307}:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-root123456}"]
      interval: 5s
      timeout: 5s
      retries: 20
    networks:
      - wechatpadpro2-network

  redis2:
    image: wechatpadpro/redis:6
    container_name: redis2
    restart: always
    env_file:
      - .env2
    volumes:
      - redis2_data:/data
    command: ["redis-server", "--appendonly", "yes", "--requirepass", "${REDIS_PASSWORD:-123456}", "--maxmemory", "512mb", "--maxmemory-policy", "volatile-lru"]
    ports:
      - "${REDIS_PORT:-6380}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD:-123456}", "ping"]
      interval: 5s
      timeout: 5s
      retries: 20
    networks:
      - wechatpadpro2-network

networks:
  wechatpadpro2-network:
    driver: bridge

volumes:
  mysql2_data:
  redis2_data:
