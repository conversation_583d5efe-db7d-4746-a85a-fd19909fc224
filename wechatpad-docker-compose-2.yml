version: "3.3"
services:
  mysql_wx2:
    # 指定容器的名称
    container_name: mysql_wxpad2
    # 指定镜像和版本
    image: mysql:8.0
    ports:
      - "3307:3306"  # 使用不同的端口
    restart: always
    # 容器日志大小配置
    logging:
      driver: "json-file"
      options:
        max-size: "5g"
    environment:
      # 配置root密码
      MYSQL_ROOT_PASSWORD: test_mysql
      MYSQL_ROOT_HOST: '%'
      MYSQL_DATABASE: wechatpadpro2
    volumes:
      # 挂载数据目录 - 使用不同的目录
      - "./mysql2/data:/var/lib/mysql"
      # 挂载配置文件目录
      - "./mysql2/config:/etc/mysql/conf.d"
    networks:
      - wechatpadpro2_network

  redis_wx2:
    # 指定容器的名称
    image: redis:alpine
    container_name: redis_wxpad2
    restart: unless-stopped
    command: redis-server --requirepass test_redis
    environment:
      - REDIS_PASSWORD=test_redis
    volumes:
      - ./redis2/data:/data  # 使用不同的目录
    ports:
      - "6382:6379"  # 使用不同的端口
    networks:
      - wechatpadpro2_network

  wechatpad2:
    # 指定容器的名称
    container_name: wechatpad2
    # 指定镜像和版本
    image: alpine:latest
    ports:
      - "9091:8849"  # 使用不同的端口
    restart: always
    depends_on:
      - mysql_wx2
      - redis_wx2
    volumes:
      - ./app2:/app # 映射数据目录，使用不同的目录
    # 指定工作目录
    working_dir: /app
    # 指定容器启动命令，执行./stay
    command: [ "/bin/sh", "-c", "chmod +x ./stay && ./stay" ]
    # 容器日志大小配置
    logging:
      driver: "json-file"
      options:
        max-size: "5g"
    # 设置时区
    environment:
      - TZ=Asia/Shanghai
      # 设置语言
      - LANG=zh_CN.UTF-8
      # 设置编码
      - LC_ALL=zh_CN.UTF-8
    networks:
      - wechatpadpro2_network

networks:
  wechatpadpro2_network:
    # 使用不同的网络名称
    driver: bridge
